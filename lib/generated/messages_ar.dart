// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'messages.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class SAr extends S {
  SAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'ThingsBoard';

  @override
  String get home => 'الرئيسية';

  @override
  String get alarms => 'التنبيهات';

  @override
  String get devices => 'الأجهزة';

  @override
  String get more => 'المزيد';

  @override
  String get customers => 'العملاء';

  @override
  String get assets => 'الأصول';

  @override
  String get auditLogs => 'سجلات التدقيق';

  @override
  String get logout => 'تسجيل خروج';

  @override
  String get login => 'تسجيل دخول';

  @override
  String get logoDefaultValue => 'شعار ثينغز بورد';

  @override
  String get loginNotification => 'تسجيل الدخول إلى حسابك';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get emailRequireText => 'البريد الإلكتروني مطلوب.';

  @override
  String get emailInvalidText => 'صيغة البريد الإلكتروني غير صحيحة.';

  @override
  String get username => 'اسم المستخدم';

  @override
  String get password => 'كلمة المرور';

  @override
  String get passwordRequireText => 'كلمة المرور مطلوبة.';

  @override
  String get passwordForgotText => 'هل نسيت كلمة المرور؟';

  @override
  String get passwordReset => 'إعادة تعيين كلمة المرور';

  @override
  String get passwordResetText => 'أدخل البريد الإلكتروني المرتبط بحسابك وسنرسل بريدًا إلكترونيًا يحتوي على رابط لإعادة تعيين كلمة المرور';

  @override
  String get requestPasswordReset => 'طلب إعادة تعيين كلمة المرور';

  @override
  String get passwordResetLinkSuccessfullySentNotification => 'تم إرسال رابط إعادة تعيين كلمة المرور بنجاح!';

  @override
  String get or => 'OR';

  @override
  String get no => 'No';

  @override
  String get yes => 'Yes';

  @override
  String get title => 'العنوان';

  @override
  String get country => 'البلد';

  @override
  String get city => 'المدينة';

  @override
  String get stateOrProvince => 'الولاية / المقاطعة';

  @override
  String get postalCode => 'الرمز البريدي';

  @override
  String get address => 'العنوان';

  @override
  String get address2 => 'العنوان 2';

  @override
  String get phone => 'الهاتف';

  @override
  String get alarmClearTitle => 'مسح التنبيه';

  @override
  String get alarmClearText => 'هل أنت متأكد أنك تريد مسح التنبيه؟';

  @override
  String get alarmAcknowledgeTitle => 'إقرار التنبيه';

  @override
  String get alarmAcknowledgeText => 'هل أنت متأكد أنك تريد الإقرار بالتنبيه؟';

  @override
  String get assetName => 'اسم الأصل';

  @override
  String get type => 'النوع';

  @override
  String get label => 'التسمية';

  @override
  String get assignedToCustomer => 'معين للعميل';

  @override
  String get auditLogDetails => 'تفاصيل سجل التدقيق';

  @override
  String get entityType => 'نوع الكيان';

  @override
  String get actionData => 'بيانات الإجراء';

  @override
  String get failureDetails => 'تفاصيل الفشل';

  @override
  String get allDevices => 'جميع الأجهزة';

  @override
  String get active => 'نشط';

  @override
  String get inactive => 'غير نشط';

  @override
  String get systemAdministrator => 'مسؤول النظام';

  @override
  String get tenantAdministrator => 'مسؤول المستأجر';

  @override
  String get customer => 'العميل';

  @override
  String get changePassword => 'تغيير كلمة المرور';

  @override
  String get currentPassword => 'كلمة المرور الحالية';

  @override
  String get currentPasswordRequireText => 'كلمة المرور الحالية مطلوبة.';

  @override
  String get currentPasswordStar => 'كلمة المرور الحالية *';

  @override
  String get newPassword => 'كلمة المرور الجديدة';

  @override
  String get newPasswordRequireText => 'كلمة المرور الجديدة مطلوبة.';

  @override
  String get newPasswordStar => 'كلمة المرور الجديدة *';

  @override
  String get newPassword2 => 'تأكيد كلمة المرور الجديدة';

  @override
  String get newPassword2RequireText => 'تأكيد كلمة المرور الجديدة مطلوب.';

  @override
  String get newPassword2Star => 'تأكيد كلمة المرور الجديدة *';

  @override
  String get passwordErrorNotification => 'كلمات المرور المدخلة يجب أن تكون متطابقة!';

  @override
  String get emailStar => 'البريد الإلكتروني *';

  @override
  String get firstName => 'الاسم الأول';

  @override
  String get firstNameUpper => 'الاسم الأول';

  @override
  String get lastName => 'الاسم الأخير';

  @override
  String get lastNameUpper => 'الاسم الأخير';

  @override
  String get profileSuccessNotification => 'تم تحديث الملف الشخصي بنجاح';

  @override
  String get passwordSuccessNotification => 'تم تغيير كلمة المرور بنجاح';

  @override
  String get notImplemented => 'لم يتم التنفيذ!';

  @override
  String get listIsEmptyText => 'القائمة فارغة حالياً.';

  @override
  String get tryAgain => 'حاول مرة أخرى';

  @override
  String get verifyYourIdentity => 'تحقق من هويتك';

  @override
  String get continueText => 'استمرار';

  @override
  String get resendCode => 'إعادة إرسال الرمز';

  @override
  String resendCodeWait(num time) {
    String _temp0 = intl.Intl.pluralLogic(
      time,
      locale: localeName,
      other: '$time ثواني',
      one: 'ثانية واحدة',
    );
    return 'إعادة إرسال الرمز في $_temp0';
  }

  @override
  String get totpAuthDescription => 'يرجى إدخال الرمز الأمني من تطبيق المصادقة الخاص بك.';

  @override
  String smsAuthDescription(Object contact) {
    return 'تم إرسال رمز أمني إلى هاتفك على الرقم $contact.';
  }

  @override
  String emailAuthDescription(Object contact) {
    return 'تم إرسال رمز أمني إلى بريدك الإلكتروني على العنوان $contact.';
  }

  @override
  String get backupCodeAuthDescription => 'يرجى إدخال أحد الرموز الاحتياطية الخاصة بك.';

  @override
  String get verificationCodeInvalid => 'صيغة الرمز غير صالحة';

  @override
  String get toptAuthPlaceholder => 'الرمز';

  @override
  String get smsAuthPlaceholder => 'رمز SMS';

  @override
  String get emailAuthPlaceholder => 'رمز البريد الإلكتروني';

  @override
  String get backupCodeAuthPlaceholder => 'الرمز الاحتياطي';

  @override
  String get verificationCodeIncorrect => 'الرمز غير صحيح';

  @override
  String get verificationCodeManyRequest => 'طلبات كثيرة للتحقق من الرمز';

  @override
  String get tryAnotherWay => 'حاول بطريقة أخرى';

  @override
  String get selectWayToVerify => 'اختر طريقة للتحقق';

  @override
  String get mfaProviderTopt => 'تطبيق المصادقة';

  @override
  String get mfaProviderSms => 'SMS';

  @override
  String get mfaProviderEmail => 'البريد الإلكتروني';

  @override
  String get mfaProviderBackupCode => 'الرمز الاحتياطي';

  @override
  String get newUserText => 'New User?';

  @override
  String get createAccount => 'Create Account';

  @override
  String get emailVerification => 'Email verification';

  @override
  String get emailVerificationSentText => 'An email with verification details was sent to the specified email address ';

  @override
  String get emailVerificationInstructionsText => 'Please follow instructions provided in the email in order to complete your sign up procedure. Note: if you haven\'t seen the email for a while, please check your \'spam\' folder or try to resend email by clicking \'Resend\' button.';

  @override
  String get resend => 'Resend';

  @override
  String get activatingAccount => 'Activating account...';

  @override
  String get accountActivated => 'Account successfully activated!';

  @override
  String get emailVerified => 'Email verified';

  @override
  String get activatingAccountText => 'Your account is currently activating.\nPlease wait...';

  @override
  String accountActivatedText(Object appTitle) {
    return 'Congratulations!\nYour $appTitle account has been activated.\nNow you can login to your $appTitle space.';
  }

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get cancel => 'Cancel';

  @override
  String get accept => 'Accept';

  @override
  String get termsOfUse => 'Terms of Use';

  @override
  String get firstNameStar => 'First name *';

  @override
  String get firstNameRequireText => 'First name is required.';

  @override
  String get lastNameStar => 'Last name *';

  @override
  String get lastNameRequireText => 'Last name is required.';

  @override
  String get createPasswordStar => 'Create a password *';

  @override
  String get repeatPasswordStar => 'Repeat your password *';

  @override
  String get imNotARobot => 'I\'m not a robot';

  @override
  String get signUp => 'Sign up';

  @override
  String get alreadyHaveAnAccount => 'Already have an account?';

  @override
  String get signIn => 'Sign In';

  @override
  String get invalidPasswordLengthMessage => 'Your password must be at least 6 characters long';

  @override
  String get confirmNotRobotMessage => 'You must confirm that you are not a robot';

  @override
  String get acceptPrivacyPolicyMessage => 'You must accept our Privacy Policy';

  @override
  String get acceptTermsOfUseMessage => 'You must accept our Terms of Use';

  @override
  String get inactiveUserAlreadyExists => 'Inactive user already exists';

  @override
  String get inactiveUserAlreadyExistsMessage => 'There is already registered user with unverified email address.\nClick \'Resend\' button if you wish to resend verification email.';

  @override
  String get assignee => 'Assignee';

  @override
  String get alarmTypes => 'Alarm types';

  @override
  String get details => 'Details';

  @override
  String get status => 'Status';

  @override
  String get severity => 'Severity';

  @override
  String get originator => 'Originator';

  @override
  String get startTime => 'Start time';

  @override
  String get duration => 'Duration';

  @override
  String get days => 'days';

  @override
  String get hours => 'hours';

  @override
  String get minutes => 'minutes';

  @override
  String get seconds => 'seconds';

  @override
  String get viewDashboard => 'View Dashboard';

  @override
  String get activity => 'Activity';

  @override
  String get addCommentMessage => 'Add a comment...';

  @override
  String get selectUser => 'Select users';

  @override
  String get assignedToMe => 'Assigned to me';

  @override
  String get clear => 'Clear';

  @override
  String get acknowledge => 'Acknowledge';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get edited => 'Edited';

  @override
  String get deleteComment => 'Delete comment';

  @override
  String get areYouSure => 'Are you sure?';

  @override
  String get noResultsFound => 'No results found';

  @override
  String get tryRefiningYourQuery => 'Please try refining your query';

  @override
  String get failedToLoadTheList => 'Failed to load the list';

  @override
  String get tryRefreshing => 'Please try refreshing';

  @override
  String get refresh => 'Refresh';

  @override
  String get users => 'Users';

  @override
  String get unassigned => 'Unassigned';

  @override
  String get failedToLoadAlarmDetails => 'Failed to load alarm details';

  @override
  String get somethingWentWrong => 'Something Went Wrong';

  @override
  String get chooseRegion => 'Choose region';

  @override
  String get selectRegion => 'Select region';

  @override
  String get northAmerica => 'North America';

  @override
  String get europe => 'Europe';

  @override
  String get northAmericaRegionShort => 'N. Virginia';

  @override
  String get europeRegionShort => 'Frankfurt';

  @override
  String get notifications => 'Notifications';

  @override
  String get deviceList => 'Device list';

  @override
  String get dashboards => 'Dashboards';

  @override
  String get updateRequired => 'Update required';

  @override
  String updateTo(Object version) {
    return 'Update to $version';
  }

  @override
  String popTitle(Object deviceName) {
    return 'Enter PIN of $deviceName to confirm proof of possession';
  }

  @override
  String get next => 'Next';

  @override
  String get confirmation => 'Confirmation';

  @override
  String get bleHelpMessage => 'To provision your new device, please make sure that your phone’s Bluetooth is turned on and within range of your new device';

  @override
  String get wifiPassword => 'Wi-Fi password';

  @override
  String wifiHelpMessage(Object deviceName) {
    return 'To continue setup of your device $deviceName, please provide your Network\'s credentials.';
  }

  @override
  String wifiPasswordMessage(Object network) {
    return 'Enter password for $network';
  }

  @override
  String get deviceNotFoundMessage => 'Devices not found. Please make sure that your phone’s Bluetooth is turned on and within range of your new device.';

  @override
  String permissionsNotEnoughMessage(Object permissions) {
    return 'You don\'t have enough permissions for \"$permissions\" to proceed. Please grant the required permissions and tap \"Try Again\".';
  }

  @override
  String get sendingWifiCredentials => 'Sending Wi-Fi credentials';

  @override
  String get confirmingWifiConnection => 'Confirming Wi-Fi connection';

  @override
  String get provisionedSuccessfully => 'Device has been successfully provisioned';

  @override
  String get returnToDashboard => 'Return to dashboard';

  @override
  String cannotEstablishSession(Object deviceName) {
    return 'Cannot establish session with device $deviceName. Please try again';
  }

  @override
  String get claimingDevice => 'Claiming device';

  @override
  String get claimingDeviceDone => 'Device claiming done';

  @override
  String get claimingMessageSuccess => 'Device has been\nsuccessfully claimed';

  @override
  String openAppSettingsToGrantPermissionMessage(Object permissions) {
    return 'You don\'t have enough permissions for \"$permissions\" to proceed. Please open app settings, grant permissions and trap \"Try Again\".';
  }
}
