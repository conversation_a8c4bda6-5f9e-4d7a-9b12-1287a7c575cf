{"appTitle": "ThingsBoard", "home": "Home", "alarms": "Alarms", "devices": "Devices", "more": "More", "customers": "Customers", "assets": "Assets", "auditLogs": "<PERSON><PERSON>", "logout": "Log Out", "login": "Log In", "logoDefaultValue": "ThingsBoard Logo", "loginNotification": "Login to your account", "email": "Email", "emailRequireText": "Email is required.", "emailInvalidText": "Invalid email format.", "username": "username", "password": "Password", "passwordRequireText": "Password is required.", "passwordForgotText": "Forgot Password?", "passwordReset": "Reset password", "passwordResetText": "Enter the email associated with your account and we'll send an email with password reset link", "requestPasswordReset": "Request password reset", "passwordResetLinkSuccessfullySentNotification": "Password reset link was successfully sent!", "or": "OR", "no": "No", "yes": "Yes", "title": "Title", "country": "Country", "city": "City", "stateOrProvince": "State / Province", "postalCode": "Zip / Postal Code", "address": "Address", "address2": "Address 2", "phone": "Phone", "alarmClearTitle": "Clear Alarm", "alarmClearText": "Are you sure you want to clear Alarm?", "alarmAcknowledgeTitle": "Acknowledge Alarm", "alarmAcknowledgeText": "Are you sure you want to acknowledge <PERSON><PERSON>?", "assetName": "Asset name", "type": "Type", "label": "Label", "assignedToCustomer": "Assigned to customer", "auditLogDetails": "Audit log details", "entityType": "Entity Type", "actionData": "Action data", "failureDetails": "Failure details", "allDevices": "All devices", "active": "Active", "inactive": "Inactive", "systemAdministrator": "System Administrator", "tenantAdministrator": "Tenant Administrator", "customer": "Customer", "changePassword": "Change Password", "currentPassword": "currentPassword", "currentPasswordRequireText": "Current password is required.", "currentPasswordStar": "Current password *", "newPassword": "newPassword", "newPasswordRequireText": "New password is required.", "newPasswordStar": "New password *", "newPassword2": "newPassword2", "newPassword2RequireText": "New password again is required.", "newPassword2Star": "New password again *", "passwordErrorNotification": "Entered passwords must be same!", "emailStar": "Email *", "firstName": "firstName", "firstNameUpper": "First Name", "lastName": "lastName", "lastNameUpper": "Last Name", "profileSuccessNotification": "Profile successfully updated", "passwordSuccessNotification": "Password successfully changed", "notImplemented": "Not implemented!", "listIsEmptyText": "The list is currently empty.", "tryAgain": "Try again", "verifyYourIdentity": "Verify your identity", "continueText": "Continue", "resendCode": "Resend code", "resendCodeWait": "Resend code in {time,plural, =1{1 second}other{{time} seconds}}", "totpAuthDescription": "Please enter the security code from your authenticator app.", "smsAuthDescription": "A security code has been sent to your phone at {contact}.", "emailAuthDescription": "A security code has been sent to your email address at {contact}.", "backupCodeAuthDescription": "Please enter one of your backup codes.", "verificationCodeInvalid": "Invalid verification code format", "toptAuthPlaceholder": "Code", "smsAuthPlaceholder": "SMS code", "emailAuthPlaceholder": "Email code", "backupCodeAuthPlaceholder": "Backup code", "verificationCodeIncorrect": "Verification code is incorrect", "verificationCodeManyRequest": "Too many requests check verification code", "tryAnotherWay": "Try another way", "selectWayToVerify": "Select a way to verify", "mfaProviderTopt": "Authenticator app", "mfaProviderSms": "SMS", "mfaProviderEmail": "Email", "mfaProviderBackupCode": "Backup code", "newUserText": "New User?", "createAccount": "Create Account", "emailVerification": "Email verification", "emailVerificationSentText": "An email with verification details was sent to the specified email address ", "emailVerificationInstructionsText": "Please follow instructions provided in the email in order to complete your sign up procedure. Note: if you haven't seen the email for a while, please check your 'spam' folder or try to resend email by clicking 'Resend' button.", "resend": "Resend", "activatingAccount": "Activating account...", "accountActivated": "Account successfully activated!", "emailVerified": "Email verified", "activatingAccountText": "Your account is currently activating.\nPlease wait...", "accountActivatedText": "Congratulations!\nYour {appTitle} account has been activated.\nNow you can login to your {appTitle} space.", "privacyPolicy": "Privacy Policy", "cancel": "Cancel", "accept": "Accept", "termsOfUse": "Terms of Use", "firstNameStar": "First name *", "firstNameRequireText": "First name is required.", "lastNameStar": "Last name *", "lastNameRequireText": "Last name is required.", "createPasswordStar": "Create a password *", "repeatPasswordStar": "Repeat your password *", "imNotARobot": "I'm not a robot", "signUp": "Sign up", "alreadyHaveAnAccount": "Already have an account?", "signIn": "Sign In", "invalidPasswordLengthMessage": "Your password must be at least 6 characters long", "confirmNotRobotMessage": "You must confirm that you are not a robot", "acceptPrivacyPolicyMessage": "You must accept our Privacy Policy", "acceptTermsOfUseMessage": "You must accept our Terms of Use", "inactiveUserAlreadyExists": "Inactive user already exists", "inactiveUserAlreadyExistsMessage": "There is already registered user with unverified email address.\nClick 'Resend' button if you wish to resend verification email.", "assignee": "Assignee", "alarmTypes": "Alarm types", "details": "Details", "status": "Status", "severity": "Severity", "originator": "Originator", "startTime": "Start time", "duration": "Duration", "days": "days", "hours": "hours", "minutes": "minutes", "seconds": "seconds", "viewDashboard": "View Dashboard", "activity": "Activity", "addCommentMessage": "Add a comment...", "selectUser": "Select users", "assignedToMe": "Assigned to me", "clear": "Clear", "acknowledge": "Acknowledge", "edit": "Edit", "delete": "Delete", "edited": "Edited", "deleteComment": "Delete comment", "areYouSure": "Are you sure?", "noResultsFound": "No results found", "tryRefiningYourQuery": "Please try refining your query", "failedToLoadTheList": "Failed to load the list", "tryRefreshing": "Please try refreshing", "refresh": "Refresh", "users": "Users", "unassigned": "Unassigned", "failedToLoadAlarmDetails": "Failed to load alarm details", "somethingWentWrong": "Something Went Wrong", "chooseRegion": "Choose region", "selectRegion": "Select region", "northAmerica": "North America", "europe": "Europe", "northAmericaRegionShort": "N. Virginia", "europeRegionShort": "Frankfurt", "notifications": "Notifications", "deviceList": "Device list", "dashboards": "Dashboards", "updateRequired": "Update required", "updateTo": "Update to {version}", "popTitle": "Enter PIN of {deviceName} to confirm proof of possession", "next": "Next", "confirmation": "Confirmation", "bleHelpMessage": "To provision your new device, please make sure that your phone’s Bluetooth is turned on and within range of your new device", "wifiPassword": "Wi-Fi password", "wifiHelpMessage": "To continue setup of your device {deviceName}, please provide your Network's credentials.", "wifiPasswordMessage": "Enter password for {network}", "deviceNotFoundMessage": "Devices not found. Please make sure that your phone’s Bluetooth is turned on and within range of your new device.", "permissionsNotEnoughMessage": "You don't have enough permissions for \"{permissions}\" to proceed. Please grant the required permissions and tap \"Try Again\".", "sendingWifiCredentials": "Sending Wi-Fi credentials", "confirmingWifiConnection": "Confirming Wi-Fi connection", "provisionedSuccessfully": "Device has been successfully provisioned", "returnToDashboard": "Return to dashboard", "cannotEstablishSession": "Cannot establish session with device {deviceName}. Please try again", "claimingDevice": "Claiming device", "claimingDeviceDone": "<PERSON><PERSON> claiming done", "claimingMessageSuccess": "Device has been\nsuccessfully claimed", "openAppSettingsToGrantPermissionMessage": "You don't have enough permissions for \"{permissions}\" to proceed. Please open app settings, grant permissions and trap \"Try Again\"."}