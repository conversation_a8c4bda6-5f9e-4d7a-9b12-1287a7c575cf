abstract class ThingsboardImage {
  static const thingsBoardWithTitle =
      'assets/images/thingsboard_with_title.svg';
  static const thingsboard = 'assets/images/thingsboard.svg';
  static const thingsboardOuter = 'assets/images/thingsboard_outer.svg';
  static const thingsboardCenter = 'assets/images/thingsboard_center.svg';
  static const dashboardPlaceholder = 'assets/images/dashboard-placeholder.svg';
  static const deviceProfilePlaceholder =
      'assets/images/device-profile-placeholder.svg';
  static const noDataImage = 'assets/images/no-data.svg';
  static const thingsboardBigLogo = 'assets/images/thingsboard_big_logo.svg';
  static const deviceProvisioning = 'assets/images/provisioning.svg';
  static const deviceProvisioningDone = 'assets/images/provisioning-done.svg';
  static const deviceProvisioningError =
      'assets/images/device-not-connected.svg';
  static const deviceNotFound = 'assets/images/device_not_found.svg';
  static const provisioningError = 'assets/images/provisioning_error.svg';
  static const connectMobile = 'assets/images/connect_mobile.svg';
  static const mobileConnectionError =
      'assets/images/mobile-connection-error.svg';

  static final oauth2Logos = <String, String>{
    'google-logo': 'assets/images/google-logo.svg',
    'github-logo': 'assets/images/github-logo.svg',
    'facebook-logo': 'assets/images/facebook-logo.svg',
    'apple-logo': 'assets/images/apple-logo.svg',
    'qr-code-logo': 'assets/images/qr_code_scanner.svg',
    'qr-code': 'assets/images/qr_code_scanner2.svg',
  };
}
